import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import * as jose from 'jose'
import { serverConfig } from '@/lib/config'

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname

  // SECURITY: Block debug endpoints in production
  if (path.startsWith('/api/debug/') && serverConfig.environment.isProduction) {
    console.warn(`🚨 SECURITY: Blocked debug endpoint access in production: ${path}`)
    return NextResponse.json({ error: 'Debug endpoints disabled in production' }, { status: 403 })
  }

  // First, check if this is an auth API path - always allow these
  if (path.startsWith('/api/auth/')) {
    return addSecurityHeaders(NextResponse.next())
  }

  // Define public paths that don't require authentication
  const publicPaths = ['/', '/student', '/admin', '/student/forgot-password']

  // Also allow reset-password and verify-otp paths without authentication
  const isResetPasswordPath = path.startsWith('/student/reset-password')
  const isVerifyOtpPath = path.startsWith('/student/verify-otp')

  const isPublicPath = publicPaths.includes(path) || isResetPasswordPath || isVerifyOtpPath

  // Get the auth tokens from cookies - separate tokens for admin and student
  const adminAuthToken = request.cookies.get('admin_auth_token')?.value
  const studentAuthToken = request.cookies.get('student_auth_token')?.value

  // Check if the admin is authenticated
  let isAdminAuthenticated = false

  if (adminAuthToken) {
    try {
      const encoder = new TextEncoder()
      const secretKey = encoder.encode(serverConfig.auth.jwtSecret || '')
      const { payload } = await jose.jwtVerify(adminAuthToken, secretKey)

      const tokenData = payload as unknown as { id: number; role: string }
      // Only consider admin authenticated if the role is 'admin' or 'super_admin'
      isAdminAuthenticated = tokenData.role === 'admin' || tokenData.role === 'super_admin'
    } catch (error) {
      console.error('[Middleware] Admin token verification failed:', error)
      isAdminAuthenticated = false
    }
  }

  // Check if the student is authenticated
  let isStudentAuthenticated = false

  if (studentAuthToken) {
    try {
      const encoder = new TextEncoder()
      const secretKey = encoder.encode(serverConfig.auth.jwtSecret || '')
      const { payload } = await jose.jwtVerify(studentAuthToken, secretKey)

      const tokenData = payload as unknown as { id: number; role: string }
      // Only consider student authenticated if the role is 'student'
      isStudentAuthenticated = tokenData.role === 'student'
    } catch (error) {
      console.error('[Middleware] Student token verification failed:', error)
      isStudentAuthenticated = false
    }
  }

  // For admin protected routes, check admin authentication
  if (path.startsWith('/admin') && !isPublicPath) {
    if (!isAdminAuthenticated) {
      return NextResponse.redirect(new URL('/admin', request.url))
    }

    // Admin is authenticated and accessing admin routes, allow access
    return addSecurityHeaders(NextResponse.next())
  }

  // For student protected routes, check student authentication
  if (path.startsWith('/student') && !isPublicPath) {
    if (!isStudentAuthenticated) {
      return NextResponse.redirect(new URL('/student', request.url))
    }

    // Student is authenticated and accessing student routes, allow access
    return addSecurityHeaders(NextResponse.next())
  }

  // Always allow access to login pages, even if the user is already authenticated in another role
  // This allows someone to be logged in as both admin and student simultaneously
  if (path === '/admin' || path === '/student') {
    // For login pages, check if the user is already authenticated in that specific role
    // and redirect to appropriate home page only for that specific role
    if (path === '/admin' && isAdminAuthenticated) {
      return NextResponse.redirect(new URL('/admin/home', request.url))
    }

    if (path === '/student' && isStudentAuthenticated) {
      return NextResponse.redirect(new URL('/student/home', request.url))
    }

    // If they're authenticated in a different role, still allow access to the login page
    return addSecurityHeaders(NextResponse.next())
  }

  // If we reach here, it's either a public path or not handled by this middleware
  return addSecurityHeaders(NextResponse.next())
}

// SECURITY: Helper function to add security headers to all responses
function addSecurityHeaders(response: NextResponse): NextResponse {
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  return response
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image).*)',
  ],
}
