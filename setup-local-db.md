# Setup Database Lokal untuk Testing

Jika Anda ingin menjalankan migrasi di database lokal terlebih dahulu untuk testing:

## Option 1: PostgreSQL dengan Docker

```bash
# Jalankan PostgreSQL dengan Docker
docker run --name postgres-staging \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=website \
  -p 5432:5432 \
  -d postgres:15

# Update .env.local
echo "DATABASE_URL=postgres://postgres:postgres@localhost:5432/website" > .env.local.local
```

## Option 2: PostgreSQL Native (jika sudah terinstall)

```bash
# Buat database
createdb website

# Update .env.local
echo "DATABASE_URL=postgres://postgres:postgres@localhost:5432/website" > .env.local.local
```

## Jalankan Migrasi

```bash
# Copy .env.local.local ke .env.local
cp .env.local.local .env.local

# Jalankan migrasi
npm run db:migrate
```

## Ke<PERSON><PERSON> ke Database Staging

Setelah testing berhasil, kembalikan .env.local ke konfigurasi staging dan jalankan migrasi di staging.

---

**Atau berikan kredensial database staging yang benar:**
- Username: ?
- Password: ?
- Database: ?
- Host: ************ (sudah benar)
- Port: 5432 (sudah benar)
