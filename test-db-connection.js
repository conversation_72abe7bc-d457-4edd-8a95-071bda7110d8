const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env' });
dotenv.config({ path: '.env.local', override: true });

console.log('DATABASE_URL:', process.env.DATABASE_URL);
console.log('Environment variables loaded successfully');

// Test connection
const postgres = require('postgres');

async function testConnection() {
  try {
    // Try with SSL disabled and longer timeout
    const client = postgres(process.env.DATABASE_URL, {
      ssl: false,
      connect_timeout: 10,
      idle_timeout: 20,
      max_lifetime: 60 * 30
    });

    console.log('🔄 Attempting database connection...');
    const result = await client`SELECT version()`;
    console.log('✅ Database connection successful!');
    console.log('PostgreSQL version:', result[0].version);

    // Test if database exists and is accessible
    const dbTest = await client`SELECT current_database()`;
    console.log('✅ Connected to database:', dbTest[0].current_database);

    await client.end();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Full error:', error);

    // Try with SSL required
    try {
      console.log('🔄 Trying with SSL required...');
      const clientSSL = postgres(process.env.DATABASE_URL, {
        ssl: { rejectUnauthorized: false },
        connect_timeout: 10
      });

      const result = await clientSSL`SELECT version()`;
      console.log('✅ Database connection successful with SSL!');
      console.log('PostgreSQL version:', result[0].version);

      await clientSSL.end();
      return true;
    } catch (sslError) {
      console.error('❌ SSL connection also failed:', sslError.message);
      return false;
    }
  }
}

testConnection();
